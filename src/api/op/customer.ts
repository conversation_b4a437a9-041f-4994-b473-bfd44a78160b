import { defHttp } from '@/utils/http/axios';
import { useGlobSetting } from '@/hooks/setting';
import type { AxiosRequestConfig } from 'axios';
import type { CustomerQueryParams, CustomerModel } from './model/customerModel';

const { apiUrl } = useGlobSetting();

enum Api {
  PageInfo = '/admin/sys/customer/pageInfo',
  Create = '/admin/sys/customer/create',
  Customer = '/admin/sys/customer',
  CustomerDetails = '/admin/sys/customer/details',
  Change = '/admin/sys/customer/change/{id}/{state}', // 修改状态
  Export = '/admin/sys/customer/export',
  BatchDelete = '/admin/sys/customer/batchDelete/{type}',
  Import = '/admin/sys/customer/import',
  Template = '/admin/sys/customer/template',
}

/**
 * @description: 获取客户分页列表
 */
export const apiGetCustomerPage = (params: CustomerQueryParams) => {
  return defHttp.get({ url: Api.PageInfo, params });
};

/**
 * @description: 获取客户列表（不分页）
 */
export const apiGetCustomerList = (params?: CustomerQueryParams) => {
  return defHttp.get({ url: Api.Customer, params });
};

/**
 * @description: 新增客户
 */
export const apiAddCustomer = (data: CustomerModel) => {
  return defHttp.post(
    { url: Api.Create, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 获取客户详情（通过ID）
 */
export const apiGetCustomerById = (id: string) => {
  return defHttp.get({ url: `${Api.CustomerDetails}/${id}` });
};

/**
 * @description: 获取客户详情（通过查询条件）
 */
export const apiGetCustomerDetails = (params: Partial<CustomerModel>) => {
  return defHttp.get({ url: Api.CustomerDetails, params });
};

/**
 * @description: 更新客户信息
 */
export const apiUpdateCustomer = (data: CustomerModel) => {
  return defHttp.put(
    { url: Api.Customer, data },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 删除客户
 */
export const apiDeleteCustomer = (id: string) => {
  return defHttp.delete(
    { url: `${Api.Customer}/${id}` },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 批量删除客户
 */
export const apiBatchDeleteCustomer = (ids: string[], type: string = '0') => {
  return defHttp.delete(
    { url: Api.BatchDelete.replace('{type}', type), data: ids },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 修改客户状态
 */
export const apiChangeCustomerState = (id: string, state: string) => {
  return defHttp.put(
    { url: Api.Change.replace('{id}', id).replace('{state}', state) },
    {
      successMessageMode: 'message',
    },
  );
};

/**
 * @description: 导出客户数据
 */
export const apiExportCustomer = (params: CustomerQueryParams) => {
  return defHttp.get(
    { url: Api.Export, params, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description: 导入客户数据
 */
export const apiImportCustomer = (params: any, config: AxiosRequestConfig) => {
  return defHttp.uploadFile({ url: Api.Import, baseURL: apiUrl, ...config }, params);
};

/**
 * @description: 下载客户导入模板
 */
export const apiDownloadCustomerTemplate = () => {
  return defHttp.get(
    { url: Api.Template, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description: 验证客户编码唯一性
 */
export const validateCustomerCode = (rule: any, value: any, isEdit: boolean): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }

    apiGetCustomerDetails({ customerCode: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('客户编码已经存在'));
      } else {
        return resolve();
      }
    });
  });
};

/**
 * @description: 验证客户名称唯一性
 */
export const validateCustomerName = (rule: any, value: any, isEdit: boolean): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }

    apiGetCustomerDetails({ customerName: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('客户名称已经存在'));
      } else {
        return resolve();
      }
    });
  });
};

/**
 * @description: 验证联系电话唯一性
 */
export const validateContactPhone = (rule: any, value: any, isEdit: boolean): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }

    apiGetCustomerDetails({ contactPhone: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('联系电话已经存在'));
      } else {
        return resolve();
      }
    });
  });
};

/**
 * @description: 验证联系邮箱唯一性
 */
export const validateContactEmail = (rule: any, value: any, isEdit: boolean): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (isEdit) {
      return resolve();
    }
    if (!value) {
      return resolve();
    }

    apiGetCustomerDetails({ contactEmail: value }).then((result) => {
      if (result !== null) {
        return reject(new Error('联系邮箱已经存在'));
      } else {
        return resolve();
      }
    });
  });
};
