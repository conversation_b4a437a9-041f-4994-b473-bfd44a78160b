/**
 * 客户信息模型
 */
export interface CustomerItemModel {
  id?: string; // 客户ID
  customerCode?: string; // 客户编码
  customerName: string; // 客户名称
  customerType?: string; // 客户类型
  contactPerson?: string; // 联系人
  contactPhone?: string; // 联系电话
  contactEmail?: string; // 联系邮箱
  address?: string; // 地址
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区县
  zipCode?: string; // 邮政编码
  website?: string; // 网站
  industry?: string; // 行业
  scale?: string; // 规模
  creditLevel?: string; // 信用等级
  taxNumber?: string; // 税号
  bankName?: string; // 开户银行
  bankAccount?: string; // 银行账号
  legalPerson?: string; // 法人代表
  registeredCapital?: number; // 注册资本
  businessLicense?: string; // 营业执照号
  establishDate?: string; // 成立日期
  status?: string; // 状态（0正常 1停用）
  remark?: string; // 备注
  createBy?: string; // 创建者
  createTime?: string; // 创建时间
  updateBy?: string; // 更新者
  updateTime?: string; // 更新时间
  delFlag?: string; // 删除标志
  tenantId?: string; // 租户ID
}

/**
 * 客户查询参数
 */
export interface CustomerQueryParams {
  current?: number; // 当前页
  size?: number; // 页面大小
  customerCode?: string; // 客户编码
  customerName?: string; // 客户名称
  customerType?: string; // 客户类型
  contactPerson?: string; // 联系人
  contactPhone?: string; // 联系电话
  contactEmail?: string; // 联系邮箱
  province?: string; // 省份
  city?: string; // 城市
  industry?: string; // 行业
  scale?: string; // 规模
  creditLevel?: string; // 信用等级
  status?: string; // 状态
  createTime?: string[]; // 创建时间范围
  tenantId?: string; // 租户ID
}

/**
 * 客户表单模型
 */
export interface CustomerModel {
  id?: string; // 客户ID
  customerCode?: string; // 客户编码
  customerName?: string; // 客户名称
  customerType?: string; // 客户类型
  contactPerson?: string; // 联系人
  contactPhone?: string; // 联系电话
  contactEmail?: string; // 联系邮箱
  address?: string; // 地址
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区县
  zipCode?: string; // 邮政编码
  website?: string; // 网站
  industry?: string; // 行业
  scale?: string; // 规模
  creditLevel?: string; // 信用等级
  taxNumber?: string; // 税号
  bankName?: string; // 开户银行
  bankAccount?: string; // 银行账号
  legalPerson?: string; // 法人代表
  registeredCapital?: number; // 注册资本
  businessLicense?: string; // 营业执照号
  establishDate?: string; // 成立日期
  status?: string; // 状态
  remark?: string; // 备注
}

/**
 * 客户列表返回结果
 */
export type CustomerListResultModel = CustomerItemModel[];

/**
 * 客户状态枚举
 */
export enum CustomerStatusEnum {
  NORMAL = '0', // 正常
  DISABLED = '1', // 停用
}

/**
 * 客户类型枚举
 */
export enum CustomerTypeEnum {
  ENTERPRISE = '1', // 企业客户
  INDIVIDUAL = '2', // 个人客户
  GOVERNMENT = '3', // 政府客户
  INSTITUTION = '4', // 事业单位
}

/**
 * 客户规模枚举
 */
export enum CustomerScaleEnum {
  LARGE = '1', // 大型
  MEDIUM = '2', // 中型
  SMALL = '3', // 小型
  MICRO = '4', // 微型
}

/**
 * 信用等级枚举
 */
export enum CreditLevelEnum {
  AAA = 'AAA', // AAA级
  AA = 'AA', // AA级
  A = 'A', // A级
  BBB = 'BBB', // BBB级
  BB = 'BB', // BB级
  B = 'B', // B级
  CCC = 'CCC', // CCC级
  CC = 'CC', // CC级
  C = 'C', // C级
  D = 'D', // D级
}
