import type { Rule as ValidationRule } from 'ant-design-vue/lib/form/interface';
import type { ComponentType } from './types';
import { useI18n } from '@/hooks/web/useI18n';
import { dateUtil } from '@/utils/dateUtil';
import { isObject } from '@/utils/is';
import { some } from 'lodash-es';

const { t } = useI18n();

/**
 * @description: 生成placeholder
 */
export function createPlaceholderMessage(component: ComponentType) {
  if (
    isComponentType(component, [
      'Input',
      'InputTextArea',
      'InputSearch',
      'InputNumber',
      'InputCountDown',
      'InputPassword',
      'InputGroup',
      'AutoComplete',
    ])
  ) {
    return t('common.inputText');
  }
  if (isComponentType(component, ['DatePicker', 'TimePicker'])) {
    return t('common.chooseText');
  }
  if (!component) {
    return undefined;
  }
  if (
    component.includes('Select') ||
    component.includes('Cascader') ||
    component.includes('Checkbox') ||
    component.includes('Radio') ||
    component.includes('Switch') ||
    component.includes('AreaList')
  ) {
    // return `请选择${label}`;
    return t('common.chooseText');
  }
  return undefined;
}

// 封装一个数组匹配字符串 100% 匹配才是 true
export function isComponentType(component: ComponentType, types: ComponentType[]) {
  return some(types, (type) => component === type);
}

const DATE_TYPE = ['DatePicker', 'MonthPicker', 'WeekPicker', 'TimePicker'];

/**
 * 上传组件
 */
export const uploadItemType: ComponentType[] = ['Upload', 'ImageUpload'];

function genType() {
  return [...DATE_TYPE, 'RangePicker', 'TimeRangePicker'];
}

export function setComponentRuleType(
  rule: ValidationRule,
  component: ComponentType,
  valueFormat: string,
) {
  if (Reflect.has(rule, 'type')) {
    return;
  }
  if (['DatePicker', 'MonthPicker', 'WeekPicker', 'TimePicker'].includes(component)) {
    rule.type = valueFormat ? 'string' : 'object';
  } else if (['RangePicker', 'CheckboxGroup'].includes(component)) {
    rule.type = 'array';
  } else if (['InputNumber'].includes(component)) {
    rule.type = 'number';
  }
}

export function processDateValue(attr: Recordable, component: string) {
  const { valueFormat, value } = attr;
  if (valueFormat) {
    attr.value = isObject(value) ? dateUtil(value as unknown as Date).format(valueFormat) : value;
  } else if (DATE_TYPE.includes(component) && value) {
    attr.value = dateUtil(attr.value);
  }
}

export const defaultValueComponents = [
  'Input',
  'InputPassword',
  'InputNumber',
  'InputSearch',
  'InputTextArea',
];

/**
 * 时间字段
 */
export const dateItemType = genType();

// TODO 自定义组件封装会出现验证问题，因此这里目前改成手动触发验证
export const NO_AUTO_LINK_COMPONENTS: ComponentType[] = [
  'Upload',
  'ApiTransfer',
  'ApiTree',
  'ApiTreeSelect',
  'ApiRadioGroup',
  'ApiCascader',
  'AutoComplete',
  'RadioButtonGroup',
  'ImageUpload',
  'ApiSelect',
  'UploadImage',
  'UploadId',
  'UploadFile',
  'InputList',
  'EvaluateRate',
  'InputPasswordPro',
  'InputZero',
];

export const simpleComponents = ['Divider', 'BasicTitle', 'DividerTitle'];

/**
 * 数组值的字段的组件
 */
export const arrayItemType = ['UploadId'];

export function isIncludeSimpleComponents(component?: ComponentType) {
  return simpleComponents.includes(component || '');
}
