<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'ACTION'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { useGo } from '@/hooks/web/usePage';
  import { ref } from 'vue';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';

  const go = useGo();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */

  const searchInfo = ref({
    status: 1,
  });

  const [registerTable, { reload }] = useTable({
    api: async () => ({}),
    columns: [],
    formConfig: {
      schemas: [],
    },
    searchInfo,
    useSearchForm: true,
    actionColumn: {},
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: [],
    addFn: () => ({}),
    updateFn: () => ({}),
  });
  const method = {
    /** 详情 */
    detail: (record: Recordable) => {
      go(`/xxx/${record.id}`);
    },
    /** 新增 */
    add: () => {
      addDrawer();
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      updateDrawer({
        record,
      });
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:eye-outlined',
        tooltip: '详情',
        onClick: method.detail.bind(null, record),
      },
      {
        icon: 'ant-design:edit-outlined',
        tooltip: '编辑',
        onClick: method.update.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
